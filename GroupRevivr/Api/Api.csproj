<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
	<PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.23.0" />
	<PackageReference Include="Microsoft.ApplicationInsights.PerfCounterCollector" Version="2.23.0" />
	<PackageReference Include="Microsoft.ApplicationInsights.Profiler.AspNetCore" Version="2.7.3" />
	<PackageReference Include="PdfSharpCore" Version="1.3.65" />
    <PackageReference Include="Azure.AI.OpenAI" Version="2.1.0" />
    <PackageReference Include="Microsoft.NETCore.Platforms" Version="7.0.4" />
    <PackageReference Include="Microsoft.NETCore.Targets" Version="5.0.0" />
    <PackageReference Include="QRCoder" Version="1.6.0" />
    <PackageReference Include="Sanity.Linq" Version="1.7.1" />
    <PackageReference Include="SendGrid" Version="9.29.3" />
    <PackageReference Include="Sentry.AspNetCore" Version="5.1.1" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageReference Include="Serilog.Enrichers.Environment" Version="3.0.1" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="9.0.0" />
    <PackageReference Include="Serilog.Sinks.ApplicationInsights" Version="4.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.Datadog.Logs" Version="0.5.5" />
    <PackageReference Include="SkiaSharp" Version="3.116.1" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="7.2.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Database\Database.csproj">
      <GlobalPropertiesToRemove></GlobalPropertiesToRemove>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Remove="Controllers\Model\" />
    <None Remove="Microsoft.EntityFrameworkCore, Version=6.0.8.0, Culture=neutral, PublicKeyToken=adb9793829ddae60" />
    <None Update="Fonts\Lato-Black.ttf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Fonts\Lato-BlackItalic.ttf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Fonts\Lato-Bold.ttf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Fonts\Lato-BoldItalic.ttf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Fonts\Lato-Italic.ttf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Fonts\Lato-Light.ttf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Fonts\Lato-LightItalic.ttf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Fonts\Lato-Regular.ttf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Fonts\Lato-Thin.ttf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Fonts\Lato-ThinItalic.ttf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Scorm\Files\imsmanifest.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Scorm\Files\index.html">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Scorm\OldFiles\imsmanifest.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Scorm\OldFiles\index.html">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Fonts\MPLUSRounded1c-Bold.ttf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Fonts\MPLUSRounded1c-Regular.ttf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Controllers\Model\" />
    <Folder Include="Fonts" />
    <Folder Include="Scorm\Files" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Microsoft.EntityFrameworkCore, Version=6.0.8.0, Culture=neutral, PublicKeyToken=adb9793829ddae60" />
  </ItemGroup>
</Project>
