using Api;
using Api.Middleware;
using Database.Data;
using Microsoft.EntityFrameworkCore;
using Serilog;
using Serilog.Events;
using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.ApplicationInsights.Extensibility.EventCounterCollector;
using Microsoft.ApplicationInsights.Extensibility.PerfCounterCollector;

var builder = WebApplication.CreateBuilder(args);
var configuration = builder.Configuration;
configuration
    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
    .AddJsonFile("appsettings.development.json", optional: true, reloadOnChange: true);
Configuration.Instance = configuration;

// Enable Application Insights
builder.Services.AddApplicationInsightsTelemetry(options =>
{
    options.EnablePerformanceCounterCollectionModule = true;
});

builder.Services.AddSingleton<ITelemetryModule, PerformanceCollectorModule>();



RateLimitingConfig.LoadConfiguration(Configuration.Instance);
var logLevel = AppsettingsHelper.GetValidSetting(configuration["Serilog:WriteTo:0:Args:logLevel"], "Error");

Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(configuration)
    .Enrich.FromLogContext()
    .Enrich.WithMachineName()
    .MinimumLevel.Is(Enum.Parse<LogEventLevel>(logLevel))
    .CreateLogger();

builder.Host.UseSerilog(); 
builder.WebHost.UseSentry();


builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

builder.Services.AddHttpClient();
// Add memory cache services
builder.Services.AddMemoryCache();

builder.Services.Configure<RateLimitingOptions>(builder.Configuration.GetSection("RateLimiting"));

// Use the helper to validate and provide a fallback value if necessary
var allowedOriginsSetting = AppsettingsHelper.GetValidSetting(builder.Configuration.GetSection(AppsettingsConstants.CorsAllowedOrigins).Value, "*");

// Parse the allowed origins
var allowedOrigins = allowedOriginsSetting.Split(",", StringSplitOptions.RemoveEmptyEntries)
    .Select(origin => origin.Trim())
    .ToArray();

// Enable Cors
builder.Services.AddCors(p => p.AddDefaultPolicy(builder =>
{
    if (allowedOrigins.Length == 1 && allowedOrigins[0] == "*")
    {
        // Allow all origins if the wildcard "*" is set
        builder.AllowAnyOrigin().AllowAnyMethod().AllowAnyHeader();
    }
    else
    {
        // Use specific origins if provided
        builder.WithOrigins(allowedOrigins)
            .AllowAnyMethod()
            .AllowAnyHeader()
            .SetIsOriginAllowedToAllowWildcardSubdomains(); // Optional: Enable subdomain support;
    }
}));

var connectionString = builder.Configuration.GetConnectionString("Database");
builder.Services.AddDbContext<ClassroomDbContext>(x => x.UseSqlServer(connectionString, sqlServer => sqlServer.MigrationsAssembly("Database")));
var app = builder.Build();


app.MapGet("/test-log", (ILogger<Program> logger) =>
{
    logger.LogInformation("Test log to Application Insights at {Time}", DateTime.UtcNow);
    return Results.Ok("Log sent");
});

app.UseSentryTracing();
// Configure the HTTP request pipeline.
// if (app.Environment.IsDevelopment())
// {
    app.UseSwagger();
    app.UseSwaggerUI();
// }

app.UseMiddleware<CustomExceptionMiddleware>();

app.UseCors();

app.UseHttpsRedirection();

app.UseAuthorization();
app.MapControllers();


using (var scope = app.Services.CreateScope())
{
    var services = scope.ServiceProvider;
    var dbContext = services.GetRequiredService<ClassroomDbContext>();

    // Apply pending migrations to the database
    dbContext.Database.Migrate();
}



app.Run();

static IConfiguration BuildConfiguration()
{
    // Build and return your IConfiguration object
    // Example:
    var configurationBuilder = new ConfigurationBuilder();
    // Add configuration sources (e.g., appsettings.json, environment variables)
    return configurationBuilder.Build();
}